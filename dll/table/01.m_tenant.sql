CREATE TABLE m_tenant (
    tenant_no bigint NOT NULL,
    tenant_id character varying(20),
    tenant_name character varying(100),
    company_name character varying(100),
    contact_email character varying(256),
    domain character varying(100),
    allowed_ip character varying(1000),
    admin_language_code character varying(2),
    language_code_list character varying(512)[],
    login_option integer DEFAULT 1 NOT NULL,
    exhibition_end_email_option integer DEFAULT 1 NOT NULL,
    function_options jsonb,
    bid_options jsonb,
    search_result_view_mode character varying(10) DEFAULT 'panel' NOT NULL,
    start_datetime timestamp with time zone DEFAULT now() NOT NULL,
    end_datetime timestamp with time zone,
    create_datetime timestamp with time zone DEFAULT now() NOT NULL,
    update_datetime timestamp with time zone DEFAULT now() NOT NULL,
    delete_flag integer DEFAULT 0 NOT NULL
);

COMMENT ON TABLE m_tenant IS 'テナントテーブル';
COMMENT ON COLUMN m_tenant.tenant_no IS 'テナント番号';
COMMENT ON COLUMN m_tenant.tenant_id IS 'テナントID';
COMMENT ON COLUMN m_tenant.tenant_name IS 'テナント名';
COMMENT ON COLUMN m_tenant.company_name IS '運営企業名';
COMMENT ON COLUMN m_tenant.contact_email IS '連絡先Eメール';
COMMENT ON COLUMN m_tenant.domain IS 'ドメイン';
COMMENT ON COLUMN m_tenant.allowed_ip IS 'テナントIPアドレス';
COMMENT ON COLUMN m_tenant.language_code_list IS '対応言語リスト';
COMMENT ON COLUMN m_tenant.login_option IS 'ログイン区分'; -- 1 member_id, 2 email
COMMENT ON COLUMN m_tenant.exhibition_end_email_option IS '入札会終了時メール送信区分'; -- 1 一斉終了, 2 随時終了
COMMENT ON COLUMN m_tenant.function_options IS '機能オプション'; -- jsonb形式で保存
COMMENT ON COLUMN m_tenant.bid_options IS '入札オプション'; -- jsonb形式で保存
COMMENT ON COLUMN m_tenant.search_result_view_mode IS '検索結果表示モード'; -- panel (パネル表示/グリッド表示), row (行表示/リスト表示)
COMMENT ON COLUMN m_tenant.start_datetime IS '適用開始日時';
COMMENT ON COLUMN m_tenant.end_datetime IS '適用終了日時';
COMMENT ON COLUMN m_tenant.create_datetime IS '作成日時';
COMMENT ON COLUMN m_tenant.update_datetime IS '更新日時';
COMMENT ON COLUMN m_tenant.delete_flag IS '削除フラグ';

CREATE SEQUENCE m_tenant_no_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;
ALTER TABLE ONLY m_tenant ALTER COLUMN tenant_no SET DEFAULT nextval(
    'm_tenant_no_seq'::regclass
);

ALTER TABLE ONLY m_tenant ADD CONSTRAINT m_tenant_pkey PRIMARY KEY (
    tenant_no
);
