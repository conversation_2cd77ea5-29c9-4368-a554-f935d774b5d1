/**
 * Test suite for useLanguage composable
 */

import {useLanguage} from '@/composables/useLanguage'
import {beforeEach, describe, expect, it, vi} from 'vitest'
import {ref} from 'vue'

// Mock dependencies
const mockLocale = ref('ja')
const mockVuetifyLocale = ref('ja')
const mockLanguageStore = {
  language: 'ja',
  setLanguage: vi.fn(),
  setLoading: vi.fn(),
}
const mockChangeLanguage = vi.fn()

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    locale: mockLocale,
  }),
}))

vi.mock('vuetify', () => ({
  useLocale: () => ({
    current: mockVuetifyLocale,
  }),
}))

vi.mock('@/stores/language', () => ({
  useLanguageStore: () => mockLanguageStore,
  default: () => ({
    changeLanguage: mockChangeLanguage,
  }),
}))

describe('useLanguage Composable', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocale.value = 'ja'
    mockVuetifyLocale.value = 'ja'
    mockLanguageStore.language = 'ja'
  })

  describe('Initialization', () => {
    it('returns correct initial values', () => {
      const {
        currentLanguage,
        availableLanguages,
        isChangingLanguage,
        changeLanguage,
        initializeLanguage,
      } = useLanguage()

      expect(currentLanguage.value).toBe('ja')
      expect(availableLanguages.value).toHaveLength(2)
      expect(isChangingLanguage.value).toBe(false)
      expect(typeof changeLanguage).toBe('function')
      expect(typeof initializeLanguage).toBe('function')
    })

    it('has correct available languages', () => {
      const {availableLanguages} = useLanguage()

      expect(availableLanguages.value).toEqual([
        {code: 'ja', label: 'Japanese', nativeLabel: 'JP'},
        {code: 'en', label: 'English', nativeLabel: 'EN'},
      ])
    })
  })

  describe('Language Changing', () => {
    it('changes language successfully', async () => {
      mockChangeLanguage.mockResolvedValue()

      const {changeLanguage, isChangingLanguage} = useLanguage()

      const changePromise = changeLanguage('en')

      // Should be loading
      expect(isChangingLanguage.value).toBe(true)

      await changePromise

      // Should call API
      expect(mockChangeLanguage).toHaveBeenCalledWith('en')
      expect(mockLanguageStore.setLanguage).toHaveBeenCalledWith('en')

      // Should update locales
      expect(mockLocale.value).toBe('en')
      expect(mockVuetifyLocale.value).toBe('en')

      // Should not be loading
      expect(isChangingLanguage.value).toBe(false)
    })

    it('does not change if same language', async () => {
      const {changeLanguage} = useLanguage()

      await changeLanguage('ja') // Same as current

      expect(mockChangeLanguage).not.toHaveBeenCalled()
    })

    it('handles errors during language change', async () => {
      const error = new Error('Language change failed')
      mockChangeLanguage.mockRejectedValue(error)

      const {changeLanguage, isChangingLanguage} = useLanguage()

      await expect(changeLanguage('en')).rejects.toThrow('Language change failed')

      // Should still reset loading state
      expect(isChangingLanguage.value).toBe(false)
    })
  })

  describe('Initialization', () => {
    it('initializes language correctly', () => {
      const {initializeLanguage} = useLanguage()

      initializeLanguage()

      expect(mockLocale.value).toBe('ja')
      expect(mockVuetifyLocale.value).toBe('ja')
    })
  })
})
