// Type definitions for API responses and data structures
export type BidStatus = {
  current_price: number
  bid_price: number
  bid_quantity: number
  tax_rate: number
  pitch_width?: number
  is_top_member?: boolean | null
  minimum_bid_exceeded?: boolean
  status: number
  can_bid: boolean
  started: boolean
  quantity: number
  extending: boolean
  is_cancel: boolean
  top_price: number
  end_datetime: string
  pitch_option: number
  is_more_little: boolean
  pitch_button_1: number
  pitch_button_2: number
  pitch_button_3: number
  start_datetime: string
  is_second_member: boolean | null
  lowest_bid_price: number
  automatic_bidding: boolean
  remaining_seconds: number
  lowest_bid_quantity: number
  top_member_nickname: string
  is_exceeding_lowest_price: boolean
  is_not_exceeding_lowest_price: boolean
  has_user_bid?: boolean
}

export type AttentionInfo = {
  bid_count: number
  favorited_count?: number
  view_count?: number
  is_favorited?: boolean
}

export type FreeField = {
  product_name?: string
  image_url?: string
  start_price?: number
  category?: string
  condition?: string
  shipping_free?: boolean
  instant_price?: number
  minimum_bid_price?: number
  maker?: string
  color?: string
}

export type BidHistory = {
  bid_price: number
  bid_quantity: number
  bid_datetime: string
  user_id?: string
}

export type RawAuctionItem = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  auction_classification?: number
  sold_out?: boolean
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories?: BidHistory[]
  status?: string
}

export type FormattedAuctionItem = RawAuctionItem & {
  link: string
  imgSrc: string
  currentPrice: string
  currentPriceTaxIncluded: string
  noOfBids: number
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  bidPrice: string
  bidQuantity: string
  bidInputError: {
    bidPrice: string | null
    bidQuantity: string | null
  }
}

export type ExhibitionGroup = {
  exhibition_no: string
  exhibition_name: string
  start_datetime: string
  end_datetime: string
}

export type ProductList = {
  all: FormattedAuctionItem[]
  exhibitionList: ExhibitionGroup[]
}

export type AuctionDetails = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  auction_classification?: number
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories: BidHistory[]
  favorite_count: number
  currentPrice: string
  currentPriceTaxIncluded: string
  bid_count: number
  endDatePart: string
  endTimePart: string
  images: string[]
  freeFields: FreeField
  productName: string
  status?: number
  view_count?: number
  postage?: string
}

// Extended AuctionDetails interface for reactive store usage
export interface ReactiveProductDetails extends Partial<AuctionDetails> {
  [key: string]: any
}

// Gallery image type
export interface GalleryImage {
  large: string
  thumb: string
  size: string
  alt?: string
}

// Auction classification constants type
export type AuctionClassification = 'ascending' | 'sealed'

// // Auction classification number mapping
// export type AuctionClassificationNumber = 1 | 2

// // Router query parameters type for auction filtering
// export interface AuctionSearchQuery {
//   auctionType?: AuctionClassification
// }
