import {type TranslationKey} from '@/language/translate'
import useChangeLanguage, {useLanguageStore} from '@/stores/language'
import {computed, ref, type Ref} from 'vue'
import {useI18n} from 'vue-i18n'
import {useLocale} from 'vuetify'

export interface LanguageOption {
  code: 'ja' | 'en'
  label: string
  nativeLabel: string
}

export type LanguageComposable = {
  currentLanguage: Ref<'ja' | 'en'>
  changeLanguage: (language: 'ja' | 'en') => Promise<void>
  availableLanguages: Ref<LanguageOption[]>
  isChangingLanguage: Ref<boolean>
  initializeLanguage: () => void
  t: (key: TranslationKey) => string
}

/**
 * Composable for managing application language state and switching
 * Integrates with vue-i18n, Vuetify locale, and language store
 * Provides TypeScript autocomplete support for translation keys
 *   eg.. const { t } = useLanguage()
 *        t('HEADER_NAV_PRODUCT_CATEGORY')  // Autocomplete works and type safe
 */
export function useLanguage(): LanguageComposable {
  const {t, locale} = useI18n()
  const {current: vuetifyLocale} = useLocale()
  const languageStore = useLanguageStore()
  const {changeLanguage: apiChangeLanguage} = useChangeLanguage()

  const isChangingLanguage = ref(false)

  const availableLanguages: LanguageOption[] = [
    {code: 'ja', label: 'Japanese', nativeLabel: 'JP'},
    {code: 'en', label: 'English', nativeLabel: 'EN'},
  ]

  const currentLanguage = computed(() => languageStore.language) as Ref<'ja' | 'en'>

  const changeLanguage = async (languageCode: 'ja' | 'en') => {
    if (languageCode === currentLanguage.value) return

    isChangingLanguage.value = true

    try {
      await apiChangeLanguage(languageCode)
      languageStore.setLanguage(languageCode)

      locale.value = languageCode

      vuetifyLocale.value = languageCode
    } catch (error) {
      console.error('Failed to change language:', error)
      throw error
    } finally {
      isChangingLanguage.value = false
    }
  }

  // Initialize on composable creation
  const initializeLanguage = () => {
    const currentLang = languageStore.language as 'ja' | 'en'
    locale.value = currentLang
    vuetifyLocale.value = currentLang
  }

  // Type-safe translation function
  const typedT = (key: TranslationKey): string => {
    return t(key as string) as string
  }

  return {
    currentLanguage,
    changeLanguage,
    availableLanguages: ref(availableLanguages),
    isChangingLanguage,
    initializeLanguage,
    t: typedT,
  }
}
