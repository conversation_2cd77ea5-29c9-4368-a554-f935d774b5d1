import {useStorage} from '@vueuse/core'
import {defineStore} from 'pinia'
import useApi from '../composables/useApi'
import {useAuthStore} from './auth'

/**
 * Get current language from local storage or browser
 * Set current language to local storage
 */
export const useLanguageStore = defineStore('language', {
  state: () => {
    const storedLanguage = useStorage('currentLanguage', null)
    const browserLang = navigator.language.split('-')[0]
    const supportedLangs = ['en', 'ja']
    const initialLang =
      storedLanguage.value || (supportedLangs.includes(browserLang) ? browserLang : 'ja')

    return {
      currentLanguage: useStorage('currentLanguage', initialLang),
      isLoading: false,
    }
  },
  actions: {
    setLanguage(lang) {
      this.currentLanguage = lang
    },
    setLoading(loading) {
      this.isLoading = loading
    },
  },
  getters: {
    language: state => state.currentLanguage,
    loading: state => state.isLoading,
  },
})

/**
 * Change language in database when user was login
 * if user was not login, change language in local storage
 */
export default function useChangeLanguage() {
  const auth = useAuthStore()
  const languageStore = useLanguageStore()
  const {apiExecute, parseHtmlResponseError} = useApi()

  const changeLanguage = async lang => {
    languageStore.setLoading(true)

    try {
      if (auth.isAuthenticated) {
        await apiExecute('private/change-language', {lang})
      }

      // Update localStorage through store
      languageStore.setLanguage(lang)
    } catch (error) {
      const parsedError = parseHtmlResponseError(error)
      console.error('Error when changing language:', parsedError)
      throw error
    } finally {
      languageStore.setLoading(false)
    }
  }

  return {changeLanguage}
}
