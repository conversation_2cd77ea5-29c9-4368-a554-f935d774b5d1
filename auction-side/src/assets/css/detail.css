@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* 前へ・次へボタン
 * *========================================== */
#item-detail .back {
  width: 1280px;
  max-width: 100%;
  margin: 2rem auto 0;
  padding: 0 3rem;
}
@media screen and (max-width: 767px) {
  #item-detail .back {
    width: 100%;
    margin: 3vw 0 2vw;
    padding: 0 4vw;
  }
}
#item-detail .back .text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  position: relative;
  width: auto;
  padding: 0.5rem 1.3rem;
  color: #427fae;
  font-size: 0.8rem;
  text-decoration: none;
  background-color: transparent;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #item-detail .back .text {
    padding: 0 4vw;
    font-size: 3vw;
  }
}
#item-detail .back .text:before {
  content: '';
  position: absolute;
  left: 3px;
  top: calc(50% + 0px);
  width: 5px;
  height: 5px;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  -webkit-transform: translateY(-50%) rotate(135deg);
  transform: translateY(-50%) rotate(135deg);
}
@media screen and (max-width: 767px) {
  #item-detail .back .text:before {
    top: calc(50% + 0.3vw);
    width: 1.2vw;
    height: 1.2vw;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
  }
}
#item-detail .back .text:hover {
  text-decoration: underline;
}
#item-detail .back .text:hover:before {
  opacity: 0.8;
}
#item-detail .container {
  padding: 0 1rem 20px;
}
@media screen and (max-width: 767px) {
  #item-detail .container {
    padding: 0 4vw 4vw;
  }
}

#main .item-name-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  position: relative;
  margin: 0 auto;
  padding: 1rem 2rem;
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap {
    margin: 0 0 0.5rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 0;
  }
}
#main .item-name-wrap .name {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 0 1rem 0 0;
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap .name {
    padding: 0;
    font-size: 4vw;
    line-height: 1.5;
  }
}
#main .item-name-wrap .tag_status {
  -webkit-box-flex: 0;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  margin: 0;
  padding: 0.1rem 0 0;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap .tag_status {
    margin: 0 0 2vw;
    padding: 2vw 0;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
  }
}
#main .item-name-wrap .tag_status p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px 14px;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 4px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main .item-name-wrap .tag_status p {
    padding: 1.5vw 4.2vw 1.7vw;
    font-size: 3vw;
    border-radius: 1vw;
  }
}
#main .item-name-wrap .tag_status .free-shipping {
  color: #fff;
  background-color: #333;
}
#main .item-name-wrap .tag_status .campaign {
  color: #fff;
  background-color: #427fae;
}
#main .tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 3px;
  width: 100%;
  margin: 0;
  padding: 0 0.2rem 0 2rem;
}
@media screen and (max-width: 767px) {
  #main .tab-wrap {
    margin: 0 0 1vw;
    padding: 0;
  }
}
#main .tab-wrap li {
  width: auto;
  margin: 0;
  padding: 1px 8px;
  border: 1px solid #333;
  font-size: 0.6rem;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media screen and (max-width: 767px) {
  #main .tab-wrap li {
    padding: 0.5vw 2vw;
    font-size: 2.2vw;
  }
}
#main .tab-wrap li.tab-main {
  color: #fff;
  background-color: #333;
}
#main .tab-wrap li.tab-sub {
  color: #333;
  border: 1px solid #333;
}
#main .tab-wrap li.tab-standard {
  color: #427fae;
  border: 1px solid #427fae;
}
#main #item-data {
  position: relative;
}
#main #item-data .item_d-main {
  width: 100%;
  margin-top: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 2vw 0 0;
  }
}
#main #item-data .item_d-main .item_d-main-visual {
  width: calc(100% - 497px);
  padding: 0 2rem;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-visual {
    width: calc(100% - 427px);
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-visual {
    width: 100%;
    padding: 0 0 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap {
  width: 100%;
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav {
  margin: 15px 0 0;
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav li {
  margin: 0 3px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav button {
  display: block;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav button {
    display: none !important;
  }
}
#main #item-data .item_d-main .item_d-main-visual .slider_wrap ul.slider-nav button:hover {
  display: block;
}
#main #item-data .item_d-main .item_d-main-txt {
  width: 497px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-line-pack: start;
  align-content: flex-start;
  padding: 0 2rem 0 1rem;
}
@media screen and (max-width: 1080px) {
  #main #item-data .item_d-main .item_d-main-txt {
    width: 427px;
    padding: 0 2rem 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt {
    width: 100%;
    padding: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode {
  width: 100%;
  margin: 0 0 1rem;
  padding: 0;
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode p {
  padding: 1.4rem 1rem;
  background-color: #000;
  border-radius: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode p {
    font-size: 3vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .bid-mode p .mode-name {
  display: inline-block;
  margin: 0 0 0 0.5rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .bid-mode p .mode-name {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data {
  width: 100%;
  border: none;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0;
  margin: 0;
  padding: 1.5rem 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price {
    padding: 5vw 0.5vw 6vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dt,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  margin: 0;
  padding: 0.2rem 0;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dt,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd {
    font-weight: 500;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dt {
  width: 120px;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dt {
    font-size: 3.5vw;
    width: 50%;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  width: calc(100% - 120px);
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd {
    font-size: 3.5vw;
    width: 50%;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-start {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-start {
    font-size: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-now {
  color: #ff0000;
  font-size: 1.2rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-now {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-buyit {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.price-buyit {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min-bid {
  font-size: 1.2rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.min-bid {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.ship {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd.ship {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .unit {
  display: inline-block;
  margin-left: 0.1rem;
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data dl.bid-price dd .unit {
    margin-left: 0.5vw;
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status {
  margin: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status {
    margin: 0 0 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span {
  display: inline-block;
  padding: 0.5rem 0;
  color: #ff0000;
  font-size: 0.8rem;
  font-weight: 600;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-status span {
    padding: 2vw 0;
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history {
  padding: 1rem 0;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history p {
  font-size: 0.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history p {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table {
  width: 100%;
  border: none;
  border-top: 1px solid #f5f5f5;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table tr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 1px solid #f5f5f5;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table tr {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td {
  padding: 10px 10px;
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td {
    padding: 2.5vw;
    font-size: 3.2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.bidder,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.bidder {
  width: 45%;
  background-color: #f8f8f8;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.bidder,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.bidder {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.bidder .name,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  .bid-history
  table
  td.bidder
  .name {
  margin-right: 5px;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  .bid-history
  table
  th.bidder
  .highest,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .item_d-main-data
  .bid-history
  table
  td.bidder
  .highest {
  display: inline;
  margin: 0;
  padding: 0 5px;
  color: #333;
  font-size: 0.6rem;
  font-weight: 600;
  border: 1px solid #333;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-data
    .bid-history
    table
    th.bidder
    .highest,
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-data
    .bid-history
    table
    td.bidder
    .highest {
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    padding: 0 2vw;
    font-size: 2.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.amount,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.amount {
  width: 30%;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.amount,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.amount {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.time,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.time {
  width: 35%;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.time,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.time {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th.time span,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table td.time span {
  text-align: right;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history table th {
  font-weight: 600;
  background-color: #f8f8f8;
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0.5rem 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 2vw 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dt,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dd {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  margin: 0 0 5px;
  padding: 0.5rem;
  font-size: 0.8rem;
  font-weight: 400;
  line-height: 1.2;
  background-color: #f5f5f5;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dt,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dd {
    height: 5.7vw;
    font-size: 3.2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dt .max-bid,
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dd .max-bid {
  display: inline;
  margin: 0 0 0 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dt .max-bid,
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dd .max-bid {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dt {
  width: calc(100% - 170px);
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dt {
    width: 100%;
    margin: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dd {
  width: 170px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-data .bid-history dl dd {
    width: 100%;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  margin: 0 0 1rem;
  padding: 1rem 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel {
    padding: 4vw 0.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel.sealed {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 10px;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status {
  height: auto;
  padding: 3px 10px;
  color: #fff;
  font-size: 0.8rem;
  font-weight: 600;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status {
    height: auto;
    font-size: 3.2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status.top {
  background-color: #e98181;
  border: 1px solid #e98181;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .status-wrap .status.overbid {
  color: #e98181;
  border: 1px solid #e98181;
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update {
  position: relative;
  width: 55px;
  height: 55px;
  margin: 0 0 0 1rem;
  padding: 1.5rem 0 0;
  color: #e98181;
  text-align: center;
  background-color: #fff;
  border: 1px solid #e98181;
  border-radius: 30px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .update {
    width: 14vw;
    height: 14vw;
    margin: 0 0 0 4vw;
    padding: 6.8vw 0 0;
    border-radius: 10vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update span {
  font-size: 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .update span {
    font-size: 2.7vw;
    font-weight: 600;
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update::after {
  content: '';
  display: inline-block;
  background: url('../img/common/icn_refresh_r.svg') center 8px no-repeat;
  background-size: 18px auto;
  background-position: center;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 8px;
  left: calc(50% - 10px);
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .status-panel .update::after {
    background-wrap: nowrap;
    background-size: 4.5vw auto;
    background-position: center;
    width: 5vw;
    height: 5vw;
    top: 2vw;
    left: calc(50% - 2.5vw);
  }
}
#main #item-data .item_d-main .item_d-main-txt .status-panel .update:hover {
  opacity: 1;
  background-color: #fff9f9;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid {
  position: relative;
  width: 100%;
  padding: 1rem 1.5rem;
  background-color: #f0f0f0;
  /* --------------------------- */
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid {
    padding: 1rem;
    border-bottom: 1px solid #fff;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  margin: 0.5rem 0 1rem;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dt {
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dt {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd {
  text-align: right;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.price {
  font-size: 1.2rem;
  font-weight: 600;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.unit {
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bidded-price dd span.unit {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl {
  margin: 0;
  padding: 0;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl span {
  display: block;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .ttl span {
    display: inline-block;
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price {
  font-size: 1rem;
  font-weight: 700;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price {
    font-size: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input {
  width: 13rem;
  margin: 0 0.5rem 0 0;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 600;
  text-align: right;
  border: none;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input {
    width: 50vw;
    height: 12vw;
    font-size: 4vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid_head
  .price
  input::-webkit-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input::-moz-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid_head
  .price
  input:-ms-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid_head
  .price
  input::-ms-input-placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input::placeholder {
  color: #ddd;
  font-size: 1.4rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input::-webkit-input-placeholder {
    font-size: 5.5vw;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input::-moz-placeholder {
    font-size: 5.5vw;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input:-ms-input-placeholder {
    font-size: 5.5vw;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid_head
    .price
    input::-ms-input-placeholder {
    font-size: 5.5vw;
  }
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price input::placeholder {
    font-size: 5.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price .unit {
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid_head .price .unit {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.5rem;
  width: 100%;
  margin: 0.8rem 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap {
    gap: 2vw;
    margin: 3vw 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li {
    width: auto;
    padding: 0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  margin: 0;
  padding: 0.2rem 0.6rem 0.2rem 0.2rem;
  font-size: 0.85rem;
  font-weight: 500;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
  background-color: #fff;
  border-radius: 4px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit {
    padding: 0.5vw 1vw 0.7vw 0.5vw;
    font-size: 3.5vw;
    border-radius: 1vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0;
  color: #fff;
  line-height: 1;
  background-color: #427fae;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .bid-unit-wrap li button.bid-unit span {
    width: 5.5vw;
    height: 5.5vw;
    margin: 0 0.5vw;
    padding: 0;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .bid-unit-wrap
  li
  button.bid-unit
  span::after {
  content: '+';
  position: absolute;
  top: 0;
  left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 1rem;
  -webkit-transform: translateY(-1px);
  transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .bid-unit-wrap
    li
    button.bid-unit
    span::after {
    width: 5.5vw;
    height: 5.5vw;
    font-size: 4.4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .note {
  width: 100%;
  font-size: 0.65rem;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .note {
    font-size: 2.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 10px;
  margin: 1.5rem 0;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 74px;
  margin: 0 auto;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  background-color: #427fae;
  border-radius: 50px;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn {
    height: 16vw;
    font-size: 4.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn:hover {
  opacity: 0.8;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .pct {
  width: 18px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .pct {
    width: 4.6vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .bid-text {
  position: relative;
  width: auto;
  display: inline-block;
  padding-left: 14px;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn .bid-text {
    font-size: 4.2vw;
    font-weight: 600;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.modal-open {
  margin: 0 0 1.5rem;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat {
  width: 60%;
  height: 44px;
  background-color: #333;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat {
    width: 100%;
    height: 12vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .pct {
  width: 20px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .pct {
    width: 4.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .text {
  display: inline-block;
  padding-left: 14px;
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .btn.chat .text {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .view_comment {
  display: inline-block;
  margin: 0 auto 1rem;
  text-decoration: none;
  color: #427fae;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .view_comment {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .btn-wrap .view_comment:hover {
  text-decoration: underline;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 40px 20px;
  overflow: auto;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 100;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .modal-container:before {
  content: '';
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .modal-container.active {
  opacity: 1;
  visibility: visible;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .modal-container .modal-body {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: calc(100% - 4rem);
  width: 1000px;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .modal-container .modal-body {
    max-width: calc(100% - 4vw);
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-close {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  top: -20px;
  right: -20px;
  width: 40px;
  height: 40px;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  background-color: #427fae;
  border-radius: 40px;
  cursor: pointer;
  z-index: 120;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content {
  position: relative;
  padding: 1rem;
  background-color: #fff;
  border-radius: 8px;
  z-index: 110;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  margin: 0;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  width: 100%;
  margin: 0;
  padding: 0 0 1rem;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure {
  width: 180px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    figure {
    width: 100%;
    height: auto;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: 100%;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  img {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
  -o-object-position: center;
  object-position: center;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status {
  position: absolute;
  bottom: 10px;
  left: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: auto;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status
  > p {
  display: inline-block;
  width: auto;
  max-width: 110px;
  margin: 2px 0;
  padding: 2px 12px;
  font-size: 0.8rem;
  font-weight: 700;
  text-align: center;
  border-radius: 20px;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status
  .status_top {
  color: #fff;
  background-color: #ff0000;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status
  .status_overbid {
  color: #ff0000;
  background-color: #d3d1d0;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status_soldout.active {
  display: block;
  position: absolute;
  top: 26%;
  left: 10%;
  width: 80%;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status_soldout.active
  img {
  width: 100%;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  figure
  .wrap_pct
  .status_soldout {
  display: none;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: calc(100% - 270px) 270px;
  grid-template-columns: calc(100% - 270px) 270px;
  grid-auto-rows: minmax(70px, auto);
  width: 100%;
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    width: 100%;
    padding: 0;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .modal-item-name {
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0.5rem 1rem 1rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  border-bottom: 1px solid #f5f5f5;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .modal-item-name {
    -ms-grid-row: 1;
    -ms-grid-row-span: 1;
    grid-row: 1/2;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    font-size: 3.8vw;
    line-height: 1.5;
    padding: 4vw 0 4vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .modal-item-name
  .tag_status {
  position: absolute;
  top: 1rem;
  right: 1rem;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .modal-item-name
    .tag_status {
    position: static;
    margin: 0 2px 2px 0;
    padding: 0.5rem 0 0;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .modal-item-name
  .tag_status
  > p {
  display: inline-block;
  padding: 4px 12px 5px;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .modal-item-name
    .tag_status
    > p {
    padding: 5px 16px 6px;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .modal-item-name
  .tag_status
  .status_recommend {
  color: #fff;
  background-color: #ff0000;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .status-panel {
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .status-panel {
    -ms-grid-row: 2;
    -ms-grid-row-span: 1;
    grid-row: 2/3;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    padding: 4vw 0;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .status-panel
  .status-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .status-panel
    .status-wrap {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell {
  -ms-grid-row: 3;
  -ms-grid-row-span: 1;
  grid-row: 3/4;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  background-color: #fff;
}
@media screen and (max-width: 1200px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell {
    -ms-grid-row: 3;
    -ms-grid-row-span: 1;
    grid-row: 3/4;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 270px;
  padding: 0 1rem;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 1200px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell
    .price {
    width: 100%;
    margin: 0 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell
    .price {
    margin: 4vw 0 2vw;
    padding: 0;
    text-align: center;
    border-bottom: 1px solid #fff;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .price-c {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell
    .price-c {
    font-size: 3.2vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .price-v {
  display: inline-block;
  margin: 0;
  color: #ff0000;
  font-size: 1.4rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell
    .price-v {
    font-size: 5.5vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status {
  width: 100%;
  padding: 0 1rem;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell
    .current-status {
    margin: 4vw 0;
    padding: 0;
    background-color: transparent;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status
  .end-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  margin: 0 0 0.5rem;
  padding: 0;
  border: none;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status
  .end-date
  img {
  width: 14px;
  height: auto;
  -webkit-transform: translateY(0px);
  transform: translateY(0px);
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status
  .other-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .sell
    .current-status
    .other-info {
    width: 100%;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status
  .other-info
  .view
  img {
  width: 16px;
  height: auto;
  margin: 0 5px 0 0;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status
  .other-info
  .favorite
  img {
  width: 14px;
  height: auto;
  margin: 0 5px 0 0;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .sell
  .current-status
  .other-info
  .bid
  img {
  width: 12px;
  height: auto;
  margin: 0 5px 0 0;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid {
  -ms-grid-row: 1;
  -ms-grid-row-span: 3;
  grid-row: 1/4;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  width: auto;
  margin: 0 0 0 1rem;
  padding: 1rem;
  background-color: #ecf2f7;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .place-bid {
    -ms-grid-row: 4;
    -ms-grid-row-span: 1;
    grid-row: 4/5;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    padding: 8vw 4vw 10vw;
    margin: 0;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  .ttl {
  width: 100%;
  margin: 0;
  font-size: 0.9rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .place-bid
    .ttl {
    font-size: 3.2vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  .price {
  width: 100%;
  font-size: 1.6rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .place-bid
    .price {
    font-size: 5.5vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  input {
  width: 4.5rem;
  margin: 0 0 0 1rem;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: right;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  input::-webkit-input-placeholder {
  color: #ddd;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  input::-moz-placeholder {
  color: #ddd;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  input:-ms-input-placeholder {
  color: #ddd;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  input::-ms-input-placeholder {
  color: #ddd;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  input::placeholder {
  color: #ddd;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 1rem 0 1rem;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  ul
  > li
  > button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 5px 5px 0;
  padding: 0 7px 0 0;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #cdcbca;
  border-radius: 30px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .place-bid
    ul
    > li
    > button {
    font-size: 1rem;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  ul
  > li
  > button
  span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0 7px;
  color: #fff;
  line-height: 1;
  background-color: #427fae;
  border-radius: 20px;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  ul
  > li
  > button
  span::after {
  content: '+';
  position: absolute;
  top: 0.7px;
  left: 5.5px;
  width: 14px;
  height: 14px;
  color: #fff;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  .other-info-detail {
  display: none;
  width: 100%;
  margin: 1rem 0 0;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .place-bid
    .other-info-detail {
    margin: 2vw 0 2vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  .other-info-detail
  button {
  position: relative;
  width: 100%;
  height: 46px;
  margin: 0;
  padding: 0.5rem 1rem;
  color: #427fae;
  font-size: 1rem;
  font-weight: 500;
  background-color: #fff;
  border: 2px solid #427fae;
  border-radius: 30px;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid
  .other-info-detail
  button::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #427fae;
  font-size: 16px;
  font-weight: 900;
  font-family: 'Material Icons';
  content: '\e5cc';
  line-height: 0.6;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid.soldout::after {
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 2;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .matching-dir
  li
  .product-wrap
  .place-bid.soldout::before {
  position: absolute;
  top: 26%;
  left: 10%;
  display: inline-block;
  content: '';
  width: 80%;
  height: 50%;
  background-image: url(../img/common/icn_soldout_list.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  z-index: 3;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .matching-dir
    li
    .product-wrap
    .place-bid.soldout::before {
    top: 15%;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .note-bid {
  width: 100%;
  padding: 1rem 1rem 0;
  font-size: 0.8rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .note-bid {
    padding: 10vw 0 0;
    font-size: 3vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .button-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .modal-container
  .modal-body
  .modal-content
  .button-bid
  button {
  width: 280px;
  max-width: calc(100% - 2rem);
  height: 56px;
  margin: 1rem auto 1rem;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 500;
  background-color: #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .modal-container
    .modal-body
    .modal-content
    .button-bid
    button {
    width: 100%;
    max-width: 100%;
    margin: 6vw auto 10vw;
    border-radius: 20vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status {
  width: 100%;
  padding: 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status {
    width: 100%;
    background-color: #f0f0f0;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  padding: 1rem 1rem;
  border-top: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date {
    padding: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date img {
  width: 18px;
  height: auto;
  margin: 0;
  -webkit-transform: translateY(0px);
  transform: translateY(0px);
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date img {
    width: 5vw;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l,
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v {
  font-size: 0.8rem;
  font-weight: 600;
  font-family: YakuHanJP, 'Noto Sans JP', Arial, sans-serif;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l,
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v {
    font-size: 3.8vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l span,
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-l span,
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .end-date .end-v span {
    font-size: 3.5vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .end-date
  .end-l
  span
  + span,
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .end-date
  .end-v
  span
  + span {
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .end-date
    .end-l
    span
    + span,
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .end-date
    .end-v
    span
    + span {
    margin-left: 2vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 1.5rem;
  width: 300px;
  margin: 0 auto;
  padding: 1rem 0 0.3rem;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info {
    width: 100%;
    padding: 4vw 4vw 2vw;
    gap: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .view img {
  width: 20px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .view img {
    width: 5vw;
  }
}
#main
  #item-data
  .item_d-main
  .item_d-main-txt
  .place-bid
  .current-status
  .other-info
  .favorite
  img {
  width: 18px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .place-bid
    .current-status
    .other-info
    .favorite
    img {
    width: 4vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .bid img {
  width: 14px;
  height: auto;
  margin: 0 5px 0 0;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info .bid img {
    width: 3.6vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info div {
  width: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
#main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info div span {
  font-size: 0.8rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .place-bid .current-status .other-info div span {
    font-size: 3.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box {
  margin: 1rem 0 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 48px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > p {
  width: auto;
  font-size: 0.65rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > p {
    font-size: 2.5vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul {
  width: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 5px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul {
    margin-left: 1vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li {
  margin-left: 10px;
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img {
  width: 30px;
}
@media screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img {
    width: 7vw;
  }
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img.x {
  width: 26px;
}
#main #item-data .item_d-main .item_d-main-txt .com-item-box #sns-share > ul > li img.instagram {
  width: 28px;
}
#main .item-note {
  width: 100%;
  max-width: 100%;
  margin: 60px auto 0;
  padding: 0 2rem;
}
@media screen and (max-width: 1080px) {
  #main .item-note {
    padding: 0 2rem;
  }
}
@media screen and (max-width: 767px) {
  #main .item-note {
    margin: 40px auto 0;
    padding: 0;
  }
}
#main .item-note h2 {
  margin: 0;
  padding: 0 0 0.8rem;
  font-size: 1.3rem;
  font-weight: 700;
  border-bottom: 1px solid #f5f5f5;
}
@media screen and (max-width: 767px) {
  #main .item-note h2 {
    height: auto;
    font-size: 5vw;
  }
}
#main .item-note .contents-wrap {
  padding: 0;
}
#main .item-note .contents-wrap h3 {
  width: 100%;
  margin: 0.8rem 0 1rem;
  padding: 0.8rem 0.8rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: left;
  background-color: #f8f8f8;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap h3 {
    font-size: 4vw;
  }
}
#main .item-note .contents-wrap p {
  margin: 1.2rem 0 0;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap p {
    font-size: 3.5vw;
  }
}
#main .item-note .contents-wrap ul li {
  padding: 0 1rem;
  text-indent: -0.8rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap ul li {
    padding: 0 4vw;
    font-size: 3.5vw;
    text-indent: -2.8vw;
  }
}
#main .item-note .contents-wrap table.spec {
  width: 100%;
  margin: 2rem 0 0;
}
#main .item-note .contents-wrap table.spec tr th,
#main .item-note .contents-wrap table.spec tr td {
  padding: 1rem 1rem;
  font-size: 0.9rem;
  background-color: #f8f8f8;
  border: 1px solid #fff;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap table.spec tr th,
  #main .item-note .contents-wrap table.spec tr td {
    padding: 4vw;
    font-size: 3.5vw;
  }
}
#main .item-note .contents-wrap table.spec tr th {
  width: 200px;
  font-weight: 700;
  white-space: nowrap;
  text-align: center;
  background-color: #ededed;
}
@media screen and (max-width: 767px) {
  #main .item-note .contents-wrap table.spec tr th {
    vertical-align: middle;
    width: 32%;
  }
}

@media only screen and (max-width: 767px) {
  #main #npNav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background-color: #ccc;
    z-index: 2;
  }
  #main #npNav a {
    font-size: 15px;
    position: relative;
    -webkit-transform: none;
    transform: none;
    width: 50%;
    height: 46px;
    border-radius: 0 !important;
    border-width: 1px;
    border-bottom: none;
    writing-mode: horizontal-tb;
    -webkit-writing-mode: horizontal-tb;
    -ms-writing-mode: horizontal-tb;
  }
  #main #npNav a + a {
    border-left: none;
  }
  #main #npNav a::after {
    position: absolute;
    top: 10px;
  }
  #main #npNav a.prev::after {
    left: 30px;
  }
  #main #npNav a.next::after {
    right: 30px;
  }
}
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *商品データ
 * *********************************************************************** */
/* スライド
 * *========================================== */
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-visual
    .slider_wrap
    ul.slider-nav
    .slick-list
    .slick-track {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100% !important;
    -webkit-transform: unset !important;
    transform: unset !important;
  }
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-visual
    .slider_wrap
    ul.slider-nav
    .slick-list
    .slick-track
    li {
    width: calc(25% - 6px) !important;
    height: auto;
    margin: 0 3px 6px;
  }
}
@media screen and (max-width: 767px) {
  #main
    #item-data
    .item_d-main
    .item_d-main-visual
    .slider_wrap
    ul.slider-nav
    .slick-list
    .slick-track
    li.slick-cloned {
    display: none;
  }
}

/* 商品情報・価格 など
 * *========================================== */
/* ---------------------------
 * *情報・価格 など
 * *----------------------------- */
/*///// 商品情報ブロック //// */
#main #item-data a.btn-back-search {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 60px auto 0;
  width: 100%;
  max-width: 360px;
  min-height: 60px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #000;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  border-radius: 100vh;
  margin-top: 60px;
  padding: 10px 20px;
}
#main #item-data a.btn-back-search img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}

#main
  #item-data.nego
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active {
  position: relative;
}
#main
  #item-data.nego
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/icn_nego.png);
  background-size: 60% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #item-data.nego .item_d-main .item_d-main-txt .place-bid:after {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 2;
}

#main
  #item-data.soldout
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active {
  position: relative;
}
#main
  #item-data.soldout
  .item_d-main
  .item_d-main-visual
  .slider_wrap
  .slick-slider
  .slick-list
  .slick-track
  figure.slick-active:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/icn_soldout.png);
  background-size: 60% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main #item-data.soldout .item_d-main .item_d-main-txt .place-bid:after {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  content: '';
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 2;
}

/*///// 価格情報ブロック //// */
/* 入札状況 */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-price .status {
    padding: 15px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-price .status p {
    margin-right: 5px;
  }
}
/* ボタン */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .btn-box a {
    font-size: 16px;
  }
}
/* 閲覧・お気に入り・入札数 */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system ul.action li + li {
    margin-left: 15%;
  }
}
/* 入札フォーム */
@media only screen and (max-width: 767px) {
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm .bidPrice {
    display: block;
  }
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm .bidPrice p.bidP-tit {
    font-size: 16px;
  }
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm .bidPrice .bidP-txt {
    font-size: 34px;
    margin-top: 5px;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-system
    .bidForm
    .bidPrice
    .bidP-txt
    span.yen {
    font-size: 26px;
  }
  #main
    #item-data
    .item_d-main
    .item_d-main-txt
    .item_d-main-system
    .bidForm
    .bidPrice
    .bidP-txt
    input.ipt-price {
    font-size: 34px;
    width: 130px;
    height: 50px;
  }
}
/*# sourceMappingURL=detail.css.map */
