<script setup lang="ts">
  import topItemImage from '@/assets/img/item/top_item01.png'
  import BidConfirmModal from '@/components/common/BidConfirmModal.vue'
  import type {AuctionDetails, ReactiveProductDetails} from '@/composables/_type'
  import useBid from '@/composables/bid'
  import {formatDateString, priceLocaleString} from '@/composables/common'
  import useFavorite from '@/composables/favorite'
  import useGetItemDetails from '@/composables/getItemDetails'
  import useApi from '@/composables/useApi'
  import useAuctionStatus from '@/composables/useAuctionStatus'
  import {PATH_NAME} from '@/defined/const'
  import {useBidConfirmStore} from '@/stores/bidConfirm'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'
  import {usePrevRouteStore} from '@/stores/prev-route'
  import {useSearchResultStore} from '@/stores/search-results'
  import {navigateToPath} from '@/utils/index.js'
  import {computed, onBeforeMount, ref, type Ref} from 'vue'
  import {useRoute, useRouter} from 'vue-router'
  import ImageGallery from './ImageGallery.vue'
  import {productImages} from './utils.ts'

  const isLoading = ref(true)
  const isManageNo = ref(null)
  // Toggle state for auction classification type
  const auctionType = ref<'ascending' | 'sealed'>('ascending')

  const route = useRoute()
  const detail = computed(() => store.productDetails as AuctionDetails)
  const store = useSearchResultStore()
  const auth = useCognitoAuthStore()
  const router = useRouter()
  const {goToPath} = usePrevRouteStore()
  const {apiExecute} = useApi()
  const {getItemDetailByExhItemNo, getConstants} = useGetItemDetails()

  // Additional stores and composables for bidding functionality
  const {productDetails} = useSearchResultStore()
  const {setPrevRoute} = usePrevRouteStore()
  const {getAuctionStatusClass} = useAuctionStatus()
  const {increaseFavoriteCount, decreaseFavoriteCount} = useFavorite()
  const bidComposable = useBid()
  const {
    bidHandle,
    addPitch,
    bidPrice: composableBidPrice,
    bidQuantity: composableBidQuantity,
  } = bidComposable
  const bidConfirmStore = useBidConfirmStore()

  // Type assertion for productDetails (since it's a reactive object from store)
  const typedProductDetails = productDetails as ReactiveProductDetails

  // Reactive variables for bidding
  const bidPrice: Ref<string> = ref('')
  const bidQuantity: Ref<string> = ref('1')

  // Toggle function for auction type
  const toggleAuctionType = () => {
    auctionType.value = auctionType.value === 'ascending' ? 'sealed' : 'ascending'
  }

  // 初期表示処理
  const getDetailData = async () => {
    isLoading.value = true
    isManageNo.value = route.params.manageNo

    // ManageNoがない場合は商品一覧に遷移
    // if (!isManageNo.value) {
    //   router.push(PATH_NAME.TOP)
    //   return
    // }

    // delete makeshop logic???
    const isFromMakeshop = !!route.query?.brand_code
    await Promise.all([
      auth.isAuthenticated ? apiExecute('private/get-change-info-constants') : null,
      getConstants(),
      await getItemDetailByExhItemNo(isManageNo.value ?? '', isFromMakeshop ? 'makeshop' : null),
    ])
    if (detail.value?.exhibition_item_no) {
      // Makeshopからアクセスがあった場合、該当の商品コードでオークション中の商品が無い場合は商品一覧に遷移させる
      if (isFromMakeshop && !detail.value.bid_status.can_bid) {
        router.push(PATH_NAME.TOP)
        return
      }
      store.setProductListForContact()
      // if (auth.isAuthenticated) {
      //   member.setMemberInfo(responses[0])
      // }
      // Initiate bid price when page is loaded
      if (detail.value.bid_status?.bid_price && detail.value.bid_status.pitch_width) {
        bidPrice.value = priceLocaleString(detail.value.bid_status.bid_price)
        // 競り上げの場合は「bidQuantity = ’1’」
        bidQuantity.value = priceLocaleString(
          detail.value.auction_classification === 1 ? '1' : detail.value.bid_status.bid_quantity
        )
      }
    } else {
      router.push(PATH_NAME.TOP)
    }
    isLoading.value = false
  }

  // Product images computed property
  // const productImages = computed(
  //   () =>
  //     typedProductDetails?.images?.map((img: any) => ({
  //       large: img?.src || img || topItemImage,
  //       thumb: img?.src || img || topItemImage,
  //       size: '1200x1200',
  //       alt: img?.alt || typedProductDetails.product_title || 'Product Image',
  //     })) || [{large: topItemImage, thumb: topItemImage, size: '1200x1200', alt: 'Default Product Image'}]
  // )

  // Helper functions
  const getRemainingTime = (endDatetime: string | null | undefined): string => {
    if (!endDatetime) return ''

    const now = new Date()
    const endTime = new Date(endDatetime)
    const diff = endTime.getTime() - now.getTime()

    if (diff <= 0) {
      return '終了'
    }

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `残り${hours}時間${minutes}分`
    } else {
      return `残り${minutes}分`
    }
  }

  const getEndDateTimeDisplay = (endDatetime: string | null | undefined): string => {
    if (!endDatetime) return ''

    const result = formatDateString(endDatetime)
    if (!result) return ''

    const {datePart, timePart} = result
    return `（${datePart} ${timePart} 終了予定）`
  }

  const incrementBidPrice = (currentPrice: string | undefined, increment: number): void => {
    const current = parseInt(currentPrice?.replace(/[^0-9]/g, '') || '0')
    bidPrice.value = (current + increment).toLocaleString()
  }

  // Handle bid price increment using the composable function
  const handleBidPriceIncrement = (increment: number): void => {
    const currentPrice = typedProductDetails.bid_status?.current_price || 0
    const pitchWidth = typedProductDetails.bid_status?.pitch_width || 1
    addPitch(currentPrice, increment, pitchWidth)
    // Sync the local bidPrice with the composable's bidPrice
    bidPrice.value = composableBidPrice.value
  }

  // Handle bid button click - now shows confirmation modal instead of direct bid
  const handleBidClick = (): void => {
    // Sync local values with composable values
    if (!bidPrice.value && !composableBidPrice.value) {
      console.log('🔵 No bid price entered!')
      return
    }

    // Use composable values if available, otherwise use local values
    const finalBidPrice = composableBidPrice.value || bidPrice.value
    const finalBidQuantity = composableBidQuantity.value || bidQuantity.value || '1'

    // Sync the composable values with our current values
    composableBidPrice.value = finalBidPrice
    composableBidQuantity.value = finalBidQuantity

    const bidParams = {
      exhibitionNo: typedProductDetails.exhibition_no || '',
      exhibitionItemNo: typedProductDetails.exhibition_item_no || '',
      exhibitionName:
        typedProductDetails.productName || typedProductDetails.freeFields?.product_name || '',
      lowestBidPrice: typedProductDetails.bid_status?.lowest_bid_price || 0,
      lowestBidQuantity: typedProductDetails.bid_status?.lowest_bid_quantity || 1,
      pitchWidth: typedProductDetails.bid_status?.pitch_width || 1,
      freeField: typedProductDetails.free_field || typedProductDetails.freeFields || {},
      newBidPrice: finalBidPrice,
      newBidQuantity: finalBidQuantity,
      currentBidPrice: typedProductDetails.bid_status?.current_price || 0,
      enteredBidPrice: typedProductDetails.bid_status?.bid_price || 0,
      enteredBidQuantity: typedProductDetails.bid_status?.bid_quantity || 0,
      maxQuantity: typedProductDetails.bid_status?.quantity || 0,
      isAscendingAuction: typedProductDetails.auction_classification === 1,
      hasUserBid: typedProductDetails.bid_status?.has_user_bid || false,
    }

    // Use bidHandle from composable which shows the confirmation modal
    bidHandle(bidParams)
  }

  // Toggle favorite function
  const toggleFavorite = (exhibitionItemNo: string | undefined): void => {
    if (!exhibitionItemNo) return

    if (typedProductDetails.attention_info?.is_favorited) {
      decreaseFavoriteCount(exhibitionItemNo)
    } else {
      increaseFavoriteCount(exhibitionItemNo)
    }
  }

  // Handle refresh after bid confirmation modal closes
  const handleRefresh = (classification?: any): void => {
    // Refresh the product details or perform any necessary updates
    console.log('Refreshing after bid confirmation', classification)
    // You can add specific refresh logic here if needed
  }

  onBeforeMount(async () => {
    await getDetailData()
  })
</script>

<template>
  <main id="main" class="">
    <div id="pNav" class="">
      <ul>
        <li><a href="/">TOP</a></li>
        <li>
          <a href="!">{{ typedProductDetails?.freeFields?.category || 'カテゴリ' }}</a>
        </li>
        <li>
          {{
            typedProductDetails?.productName || typedProductDetails?.freeFields?.product_name || ''
          }}
        </li>
      </ul>
    </div>
    <section id="item-detail">
      <div class="back"><button class="text">一覧に戻る</button></div>
      <div class="container">
        <div class="item-name-wrap">
          <p class="name">
            {{
              typedProductDetails?.productName ||
              typedProductDetails?.freeFields?.product_name ||
              ''
            }}
          </p>
          <div class="tag_status">
            <p class="free-shipping" v-if="typedProductDetails?.status === 2">New</p>
            <p class="free-shipping" v-if="typedProductDetails?.freeFields?.shipping_free">
              送料無料
            </p>
            <!--<p class="campaign">キャンペーン対象</p>-->
          </div>
        </div>
        <ul class="tab-wrap">
          <li class="tab-main" v-if="typedProductDetails?.freeFields?.condition">
            {{ typedProductDetails?.freeFields?.condition }}
          </li>
          <li class="tab-sub" v-if="typedProductDetails?.freeFields?.maker">
            {{ typedProductDetails?.freeFields?.maker }}
          </li>
          <li class="tab-wari" v-if="typedProductDetails?.freeFields?.color">
            {{ typedProductDetails?.freeFields?.color }}
          </li>
        </ul>
        <div id="item-data">
          <div class="item_d-main">
            <div class="item_d-main-visual">
              <ImageGallery
                :images="productImages"
                gallery-class="my-gallery"
                slider-main-class="slider-for"
                slider-nav-class="slider-nav"
                :nav-slides-to-show="7"
                :nav-responsive="[{breakpoint: 767, settings: {slidesToShow: 4}}]"
              />
            </div>

            <div class="item_d-main-txt">
              <div class="item_d-main-data">
                <div class="bid-mode">
                  <p>
                    <span class="mode-name">{{
                      typedProductDetails?.auction_classification === 1
                        ? '競り上がり式オークション'
                        : '封印入札式オークション'
                    }}</span>
                  </p>
                </div>
                <dl class="bid-price">
                  <dt class="price-start">スタート価格</dt>
                  <dd class="price-start">
                    {{ priceLocaleString(typedProductDetails?.freeFields?.start_price) || '0' }}
                    <span class="unit">円</span>
                  </dd>
                  <dt class="price-now">現在価格</dt>
                  <dd class="price-now">
                    {{ priceLocaleString(typedProductDetails?.bid_status?.current_price) || '0' }}
                    <span class="unit">円</span>
                  </dd>
                  <dt class="price-buyit" v-if="typedProductDetails?.freeFields?.instant_price">
                    即決価格
                  </dt>
                  <dd class="price-buyit" v-if="typedProductDetails?.freeFields?.instant_price">
                    {{ priceLocaleString(typedProductDetails?.freeFields?.instant_price) }}
                    <span class="unit">円</span>
                  </dd>
                  <dt class="min" v-if="typedProductDetails?.bid_status?.lowest_bid_price">
                    最低落札価格
                  </dt>
                  <dd class="min" v-if="typedProductDetails?.bid_status?.lowest_bid_price">
                    {{ priceLocaleString(typedProductDetails?.bid_status?.lowest_bid_price) }}
                    <span class="unit">円</span>
                  </dd>
                  <dt class="ship">送料</dt>
                  <dd class="ship">
                    {{
                      typedProductDetails?.freeFields?.shipping_free
                        ? '無料'
                        : typedProductDetails?.postage || '2,200'
                    }}
                    <span class="unit" v-if="!typedProductDetails?.freeFields?.shipping_free"
                      >円</span
                    >
                  </dd>
                </dl>
                <div class="bid-status" v-if="typedProductDetails?.bid_status?.extending">
                  <span>オークション延長中</span>
                </div>
                <div class="bid-history">
                  <p>入札履歴</p>

                  <table cellspacing="0" cellpadding="0">
                    <tbody>
                      <tr>
                        <th class="bidder">入札者</th>
                        <th class="amount">入札額</th>
                        <th class="time">最終入札時刻</th>
                      </tr>
                      <tr
                        v-for="(history, index) in typedProductDetails?.bid_histories?.slice(0, 5)"
                        :key="index"
                      >
                        <td class="bidder">
                          <span class="name">{{ history.user_id || 'ユーザー' }}</span>
                          <span class="highest" v-if="index === 0">最高額入札者</span>
                        </td>
                        <td class="amount">{{ priceLocaleString(history.bid_price) }}円</td>
                        <td class="time">
                          <span>{{ formatDateString(history.bid_datetime)?.datePart }}</span>
                          <span>{{ formatDateString(history.bid_datetime)?.timePart }}</span>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          !typedProductDetails?.bid_histories ||
                          typedProductDetails?.bid_histories?.length === 0
                        "
                      >
                        <td colspan="3" style="text-align: center; padding: 20px">
                          入札履歴がありません
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="status-panel">
                <div class="status-wrap">
                  <p class="status top" v-if="typedProductDetails?.bid_status?.is_top_member">
                    あなたがTOP
                  </p>
                  <p
                    class="status overbid"
                    v-if="typedProductDetails?.bid_status?.minimum_bid_exceeded"
                  >
                    最低落札超え
                  </p>
                </div>
                <p class="update"><span>更新</span></p>
              </div>
              <div class="place-bid">
                <dl class="bidded-price" v-if="typedProductDetails?.bid_status?.bid_price">
                  <dt>入札済み価格</dt>
                  <dd>
                    <span class="price"
                      >{{ priceLocaleString(typedProductDetails?.bid_status?.bid_price)
                      }}<span class="unit">円</span></span
                    >
                  </dd>
                </dl>
                <div class="bid_head">
                  <p class="ttl"><span>入札価格</span></p>
                  <p class="price">
                    <input
                      type="text"
                      data-id="price-bid"
                      v-model="bidPrice"
                      class="price-bid"
                      :placeholder="
                        priceLocaleString(typedProductDetails?.bid_status?.current_price) || '1,000'
                      "
                      @input="composableBidPrice = bidPrice"
                    />
                    <span class="unit">円</span>
                  </p>
                </div>
                <ul class="bid-unit-wrap">
                  <li>
                    <button class="bid-unit" @click="handleBidPriceIncrement(10000)">
                      <span class="icn_add"></span>10,000円
                    </button>
                  </li>
                  <li>
                    <button class="bid-unit" @click="handleBidPriceIncrement(50000)">
                      <span class="icn_add"></span>50,000円
                    </button>
                  </li>
                  <li class="last">
                    <button class="bid-unit" @click="handleBidPriceIncrement(100000)">
                      <span class="icn_add"></span>100,000円
                    </button>
                  </li>
                </ul>
                <p class="note">※単位で入札できます。</p>
                <div class="btn-wrap">
                  <button class="btn modal-open" @click="handleBidClick" :disabled="!bidPrice">
                    <img class="pct" src="@/assets/img/common/icn_bid_detail.svg" /><span
                      class="bid-text"
                      >入札する</span
                    >
                  </button>
                  <button
                    class="btn chat"
                    @click="navigateToPath('/details/chat/4', goToPath, $route)"
                  >
                    <img class="pct" src="@/assets/img/common/icn_chat_detail.svg" />
                    <span class="text">チャットで質問する</span>
                  </button>
                  <a class="view_comment">質問コメントを見る</a>
                </div>
                <!--ModalBid Start-->
                <div class="modal-container">
                  <div class="modal-body">
                    <div class="modal-close">×</div>
                    <div class="modal-content">
                      <ul class="matching-dir">
                        <li>
                          <figure>
                            <div class="wrap_pct">
                              <img :src="typedProductDetails?.images?.[0] || topItemImage" alt="" />
                            </div>
                          </figure>
                          <div class="product-wrap">
                            <div class="modal-item-name">
                              {{
                                typedProductDetails?.productName ||
                                typedProductDetails?.freeFields?.product_name ||
                                ''
                              }}
                            </div>
                            <div class="status-panel">
                              <div class="status-wrap">
                                <p
                                  class="status top"
                                  v-if="typedProductDetails?.bid_status?.is_top_member"
                                >
                                  あなたがTOP
                                </p>
                                <p
                                  class="status overbid"
                                  v-if="typedProductDetails?.bid_status?.minimum_bid_exceeded"
                                >
                                  最低落札超え
                                </p>
                              </div>
                            </div>
                            <div class="sell">
                              <div class="price">
                                <span class="price-c">現在価格</span
                                ><span class="price-v"
                                  >{{
                                    priceLocaleString(
                                      typedProductDetails?.bid_status?.current_price
                                    ) || '0'
                                  }}円</span
                                >
                              </div>
                              <div class="current-status">
                                <div class="end-date">
                                  <img src="@/assets/img/common/icn_clock_list.png" />
                                  <div class="end-l">終了予定</div>
                                  <div class="end-v">
                                    <span>{{ typedProductDetails?.endDatePart }}</span
                                    ><span>{{ typedProductDetails?.endTimePart }}</span>
                                  </div>
                                </div>
                                <div class="other-info">
                                  <div class="view">
                                    <img src="@/assets/img/common/icn_eye_list.svg" />
                                    <span>{{ typedProductDetails?.view_count || 0 }}</span>
                                  </div>
                                  <div class="favorite">
                                    <img src="@/assets/img/common/icn_favorite.svg" />
                                    <span>{{ typedProductDetails?.favorite_count || 0 }}</span>
                                  </div>
                                  <div class="bid">
                                    <img src="@/assets/img/common/icn_bid.svg" />
                                    <span>{{ typedProductDetails?.bid_count || 0 }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="place-bid">
                              <p class="ttl">入札価格</p>
                              <p class="price">{{ bidPrice || '1,000,000' }}円</p>

                              <div class="other-info-detail">
                                <button class="detail">商品詳細を見る</button>
                              </div>
                            </div>
                          </div>
                        </li>
                      </ul>
                      <p class="note-bid">お間違いなければ入札ボタンをクリックしてください。</p>
                      <div class="button-bid">
                        <button class="btn">入札する</button>
                      </div>
                    </div>
                  </div>
                </div>
                <!--ModalBid End-->
                <div class="current-status">
                  <div class="end-date">
                    <img src="@/assets/img/common/icn_clock_list.png" />
                    <div class="end-l">終了予定</div>
                    <div class="end-v">
                      <span>{{ typedProductDetails?.endDatePart }}</span
                      ><span>{{ typedProductDetails?.endTimePart }}</span>
                    </div>
                  </div>
                  <div class="other-info">
                    <div class="view">
                      <img src="@/assets/img/common/icn_eye_list.svg" /><span>{{
                        typedProductDetails?.view_count || 0
                      }}</span>
                    </div>
                    <div class="favorite">
                      <img src="@/assets/img/common/icn_favorite_detail.svg" /><span>{{
                        typedProductDetails?.favorite_count || 0
                      }}</span>
                    </div>
                    <div class="bid">
                      <img src="@/assets/img/common/icn_bid.svg" /><span>{{
                        typedProductDetails?.bid_count || 0
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="com-item-box">
                <div id="sns-share">
                  <p>この商品をシェア</p>
                  <ul>
                    <li>
                      <a href="A"
                        ><img
                          src="@/assets/img/common/icn_sns_facebook.svg"
                          alt="Facebook"
                          class="facebook"
                      /></a>
                    </li>
                    <li>
                      <a href="A"
                        ><img src="@/assets/img/common/icn_sns_x.svg" alt="twitter" class="x"
                      /></a>
                    </li>
                    <li>
                      <a href="A"
                        ><img
                          src="@/assets/img/common/icn_sns_instagram.svg"
                          alt="Instagram"
                          class="instagram"
                      /></a>
                    </li>
                  </ul>
                </div>
                <p
                  class="fav-mark"
                  @click="toggleFavorite(typedProductDetails?.exhibition_item_no)"
                >
                  <span>お気に入り</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="item-note">
          <h2>商品説明</h2>
          <div class="contents-wrap">
            <h3>ご購入前にご確認ください</h3>
            <p>【商品及び掲載価格について】</p>
            <ul>
              <li>
                ・&thinsp;掲載している商品価格は銀行振込、または店頭現金払いでの特別価格（税込）となっております。
                その他のお支払い方法をご希望の場合はお問合せください。
              </li>
              <li>
                ・&thinsp;メーカーよりアナウンスがなくモデルのマイナーチェンジ等が行われたり、付属品の仕様等が変更になる場合がございます。
              </li>
              <li>
                ・&thinsp;売り切れの商品に関しましては、前回販売時の価格になります。
                ご要望を頂ければお探し致しますが、お探しの商品が見つかった時点での販売価格のご案内となりますのでご了承下さい。
              </li>
            </ul>
            <p>【商品画像について】</p>
            <ul>
              <li>
                ・&thinsp;新品商品の商品画像は、初回に入荷した商品の画像となり、在庫個々の撮影はしておりません。
                あくまでも参考画像となりますのでご了承下さい。
              </li>
              <li>
                ・&thinsp;お使いのPCやタブレット、スマートフォンの設定などによって実際の商品と色合いが異なる場合がございます。
              </li>
              <li>
                ・&thinsp;シールの有無や付属品等、気になる点がございましたら、必ずご購入手続き前にお問合せ、ご確認ください。
              </li>
            </ul>

            <table class="spec">
              <tbody>
                <tr class="">
                  <th>商品名</th>
                  <td>
                    {{
                      typedProductDetails?.productName ||
                      typedProductDetails?.freeFields?.product_name ||
                      ''
                    }}
                  </td>
                </tr>
                <tr v-if="typedProductDetails?.item_no">
                  <th>型番</th>
                  <td>{{ typedProductDetails?.item_no }}</td>
                </tr>
                <tr v-if="typedProductDetails?.freeFields?.condition">
                  <th>{{ typedProductDetails?.freeFields?.condition }}</th>
                  <td>店頭展示品、未使用品、目立った傷汚れなし</td>
                </tr>
                <tr>
                  <th>配送方法</th>
                  <td>宅急便（ヤマト運輸）</td>
                </tr>
                <tr>
                  <th>送料</th>
                  <td>
                    {{ typedProductDetails?.freeFields?.shipping_free ? '無料' : '着払い' }}（<a
                      href="/"
                      >配送方法の詳細</a
                    >）
                  </td>
                </tr>
                <tr v-if="typedProductDetails?.freeFields?.maker">
                  <th>メーカー</th>
                  <td>{{ typedProductDetails?.freeFields?.maker }}</td>
                </tr>
                <tr v-if="typedProductDetails?.freeFields?.color">
                  <th>カラー</th>
                  <td>{{ typedProductDetails?.freeFields?.color }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
    <!-- Bid Confirmation Modal - TEMPORARILY DISABLED -->
    <BidConfirmModal
      v-if="false"
      v-model="bidConfirmStore.showBidConfirm"
      :isAscendingAuction="true"
      @refresh="handleRefresh"
    />
  </main>
</template>

<style lang="css" scoped>
  .thumbnail-item {
    padding: 0 3px !important;
  }

  .auction-type-switch {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 0;
    margin-bottom: 2rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .switch-label {
    margin-right: 1rem;
    font-weight: 500;
    color: #333;
    font-size: 1rem;
  }

  .switch-buttons {
    display: flex;
    background-color: #fff;
    border: 2px solid #427fae;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(66, 127, 174, 0.1);
  }

  .switch-btn {
    padding: 0.5rem 1.5rem;
    border: none;
    background-color: #fff;
    color: #427fae;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .switch-btn:hover:not(:disabled) {
    background-color: rgba(66, 127, 174, 0.1);
  }

  .switch-btn.is-active {
    background-color: #427fae;
    color: #fff;
    cursor: default;
  }

  .switch-btn:disabled {
    cursor: default;
  }

  /* Responsive design */
  @media screen and (max-width: 767px) {
    .auction-type-switch {
      flex-direction: column;
      align-items: center;
      padding: 1rem;
    }

    .switch-label {
      margin-right: 0;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .switch-btn {
      padding: 0.4rem 1rem;
      font-size: 0.8rem;
    }
  }
</style>
