<script setup lang="ts">
  import BreadCrumb from '@/components/common/BreadCrumb.vue'
  import {usePrevRouteStore} from '@/stores/prev-route'
  import {navigateToPath} from '@/utils'

  const {goToPath} = usePrevRouteStore()
</script>

<template>
  <main id="main">
    <div id="pNav" class="bgLGray">
      <BreadCrumb customTitle="お問い合わせチャット" />
    </div>
    <h2 class="page-ttl mypage">
      <p class="ttl">お問い合わせチャット</p>
      <p class="sub">Chat</p>
    </h2>
    <section id="chat-contact">
      <div class="container">
        <!-- 商品についてのお問い合わせチャット Start -->
        <div class="chat-item">
          <!--<h3>商品についてのお問い合わせ</h3>-->
          <!--<p class="note">※以下の商品に関してのみ、お問い合わせを投稿いただけます。</p>-->
          <div class="chat-head">
            <p class="label">商品名</p>
            <div class="item-desc">
              <div class="item-name-b">
                <p class="name">
                  LOUIS VUITTON ルイヴィトン モノグラム サックプラ M51140 ハンドバッグ トートバッグ
                  バッグ レディース メンズ
                </p>
              </div>
              <div class="back-item-detail">
                <button @click="navigateToPath('/details/4', goToPath, $route)">
                  <span>商品に戻る</span>
                </button>
              </div>
            </div>
          </div>
          <div class="chat-body">
            <div class="chat-body-head">
              <div class="title"><p>チャットルーム</p></div>
              <div class="btn-wrap">
                <button class="refresh"><span>更新</span></button>
              </div>
            </div>
            <div class="chat-body-wrap">
              <div class="chat-body-detail question">
                <p class="chat-body-detail-q">GMO太郎さん</p>
                <p class="chat-body-detail-text user">送料はいくらですか？</p>
              </div>
              <div class="chat-body-detail answer">
                <p class="chat-body-detail-a">ショップ</p>
                <p class="chat-body-detail-text seller">
                  お問い合わせありがとうございます。メイク二郎さんからの1回目の回答内容が入ります。メイク二郎さんからの1回目の回答内容が入ります。メイク二郎さんからの1回目の回答内容が入ります。メイク二郎さんからの1回目の回答内容が入ります。
                </p>
              </div>
              <div class="chat-body-detail answer">
                <p class="chat-body-detail-a">ショップ</p>
                <p class="chat-body-detail-text seller">
                  お問い合わせありがとうございます。メイク二郎さんからの2回目の回答内容が入ります。
                </p>
              </div>
              <div class="chat-body-detail question">
                <p class="chat-body-detail-q">flowerさん</p>
                <p class="chat-body-detail-text concealed">
                  投稿内容がポリシーに反すると判断されたため、非表示としています。
                </p>
              </div>
              <div class="chat-body-detail answer">
                <p class="chat-body-detail-a">ショップ</p>
                <p class="chat-body-detail-text seller">
                  ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。
                </p>
              </div>
              <div class="chat-body-detail question">
                <p class="chat-body-detail-q">ひでさん</p>
                <p class="chat-body-detail-text user">
                  ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。
                </p>
              </div>
              <div class="chat-body-detail answer">
                <p class="chat-body-detail-a">ショップ</p>
                <p class="chat-body-detail-text seller">
                  詳細については２枚目の画像でご確認いただけます。
                </p>
              </div>
              <div class="chat-body-detail answer">
                <p class="chat-body-detail-a">ショップ</p>
                <p class="chat-body-detail-text seller">
                  コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2コメント2
                </p>
              </div>
              <div class="chat-body-detail question">
                <p class="chat-body-detail-q">桃太郎さん</p>
                <p class="chat-body-detail-text user">
                  ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。
                </p>
              </div>
              <div class="chat-body-detail answer">
                <p class="chat-body-detail-a">ショップ</p>
                <p class="chat-body-detail-text seller">
                  ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。ご質問内容がはいります。
                </p>
              </div>
            </div>
          </div>
          <div class="chat-foot">
            <form class="chat-form" action="">
              <div class="chat-form-textarea">
                <textarea name="" id="" placeholder="メッセージを入力する"></textarea>
              </div>
              <div class="chat-form-note">
                <ul>
                  <li>※名前やメールアドレスなどの個人情報を入力しないようご注意ください。</li>
                  <li>※投稿内容は他のユーザーにも公開されます。</li>
                  <li>
                    ※送信する前に入力内容をよく確認してください。一度掲載された投稿内容は、編集および削除できません。
                  </li>
                </ul>
              </div>
              <div class="chat-form-btnWrap">
                <button class="chat-form-btn" type="submit">メッセージを投稿する</button>
                <button class="chat-form-btn clear" type="reset">クリア</button>
              </div>
            </form>
          </div>
        </div>
        <!-- 商品についてのお問い合わせチャット End -->
      </div>
    </section>
  </main>
</template>
