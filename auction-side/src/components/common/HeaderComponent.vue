<script setup>
  import {useLanguage} from '@/composables/useLanguage'
  import {PATH_NAME} from '@/defined/const'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'
  import {RouterLink, useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'

  const auth = useCognitoAuthStore()
  const router = useRouter()
  const {currentLanguage, changeLanguage, isChangingLanguage} = useLanguage()
  const {t} = useLocale()

  const handleLogout = () => {
    auth.logout()
  }

  const handleLanguageChange = async event => {
    const selectedLanguage = event.target.value
    try {
      await changeLanguage(selectedLanguage)
    } catch (error) {
      console.error('Failed to change language:', error)
    }
  }
</script>

<template>
  <!-- Header -->
  <header>
    <!-- gNav PC/SP start -->
    <div class="wrap-header-elm">
      <div class="l-header-info-links">
        <ul class="l-header-info-item">
          <li class="language-swich">
            <div class="lang-wrap">
              <select
                id="locale-switcher"
                class="lang"
                :value="currentLanguage"
                @change="handleLanguageChange"
                :disabled="isChangingLanguage"
              >
                <option value="ja" :selected="currentLanguage === 'ja'">JP</option>
                <option value="en" :selected="currentLanguage === 'en'">EN</option>
              </select>
            </div>
          </li>
        </ul>
      </div>
      <div class="main-nav-wrap">
        <div class="h-top">
          <p class="btnMenu only_sp"><span class="ham"></span></p>
          <h1 class="h-top-logo">
            <RouterLink class="logo" :to="PATH_NAME.TOP">
              <img src="@/assets/img/common/logo_cecauction.png" alt="CEC AUCTION" />
            </RouterLink>
          </h1>
          <div class="h-top-menu only_sp">
            <div class="lang-wrap">
              <select
                id="locale-switcher-sp"
                class="lang"
                :value="currentLanguage"
                @change="handleLanguageChange"
                :disabled="isChangingLanguage"
              >
                <option value="ja" :selected="currentLanguage === 'ja'">JP</option>
                <option value="en" :selected="currentLanguage === 'en'">EN</option>
              </select>
            </div>
          </div>
        </div>
        <div class="nav-elm">
          <div class="search-elm only_pc">
            <div class="search-category">
              <li>
                <a href="#" class="nav-label">{{ t('HEADER_NAV_PRODUCT_CATEGORY') }}</a>
                <div class="menu-list">
                  <p class="arrow-box"></p>
                  <div class="panel-wrap">
                    <div class="category-box">
                      <div class="category-all">
                        <p>
                          <a>{{ t('HEADER_NAV_ALL_CATEGORIES') }}</a>
                        </p>
                        <p>
                          <a>{{ t('HEADER_NAV_ASCENDING_AUCTION') }}</a>
                        </p>
                        <p>
                          <a>{{ t('HEADER_NAV_SEALED_AUCTION') }}</a>
                        </p>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a>{{ t('HEADER_NAV_CATEGORY_1') }}</a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li><a href="./list/store-fixture/">かばん・バッグ</a></li>
                          <li><a href="./list/store-fixture/">財布</a></li>
                          <li><a href="./list/store-fixture/">靴</a></li>
                          <li><a href="./list/store-fixture/">サングラス</a></li>
                          <li><a href="./list/store-fixture/">アクセサリー</a></li>
                          <li><a href="./list/store-fixture/">ジャケット・ウェア</a></li>
                          <li><a href="./list/store-fixture/">帽子</a></li>
                          <li><a href="./list/store-fixture/">メンズライン</a></li>
                        </ul>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a>{{ t('HEADER_NAV_CATEGORY_2') }}</a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li><a href="./list/store-fixture/">LOUIS VUITTON</a></li>
                          <li><a href="./list/store-fixture/">CHANEL</a></li>
                          <li><a href="./list/store-fixture/">HERMES</a></li>
                          <li><a href="./list/store-fixture/"> GUCCI</a></li>
                          <li><a href="./list/store-fixture/">PRADA</a></li>
                          <li><a href="./list/store-fixture/">BURBERRY</a></li>
                          <li><a href="./list/store-fixture/">FENDI</a></li>
                          <li><a href="./list/store-fixture/">CELINE</a></li>
                          <li><a href="./list/store-fixture/">Christian Dior</a></li>
                          <li><a href="./list/store-fixture/">ETRO</a></li>
                        </ul>
                      </div>
                    </div>
                    <div class="category-box">
                      <div class="category-top">
                        <p>
                          <a>{{ t('HEADER_NAV_CATEGORY_3') }}</a>
                        </p>
                      </div>
                      <div class="category-secondary">
                        <ul class="list-secondary">
                          <li><a href="./list/store-fixture/">ハンドバッグ・2way</a></li>
                          <li><a href="./list/store-fixture/">ショルダーバッグ</a></li>
                          <li><a href="./list/store-fixture/">トートバッグ</a></li>
                          <li><a href="./list/store-fixture/">メンズバッグ</a></li>
                          <li><a href="./list/store-fixture/">ポーチ</a></li>
                          <li><a href="./list/store-fixture/">エコバッグ</a></li>
                          <li><a href="./list/store-fixture/">セカンドバッグ</a></li>
                          <li><a href="./list/store-fixture/">バスケット・かご</a></li>
                          <li><a href="./list/store-fixture/">クラッチバッグ</a></li>
                          <li><a href="./list/store-fixture/">スーツケース</a></li>
                          <li><a href="./list/store-fixture/">バニティバッグ</a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </div>
            <div class="search-keyword">
              <input
                type="text"
                data-id="shop-search-keyword"
                value=""
                class="side-search-keyword search-keyword"
                :placeholder="t('HEADER_NAV_SEARCH_PLACEHOLDER')"
              />
              <button><img src="@/assets/img/common/icn_search_gray.svg" /></button>
            </div>
            <div class="info-menu">
              <li>
                <a href="#" class="nav-label">{{ t('HEADER_NAV_SITE_ABOUT') }}</a>
                <div class="menu-list">
                  <p class="arrow-box"></p>
                  <ul>
                    <li class="#">
                      <a href="./other/visitor/">{{ t('HEADER_NAV_FIRST_TIME_VISITORS') }}</a>
                    </li>
                    <li class="#">
                      <a href="./other/guide/">{{ t('HEADER_NAV_SHOPPING_GUIDE') }}</a>
                    </li>
                    <li class="#">
                      <a href="./other/faq/">{{ t('HEADER_NAV_FAQ') }}</a>
                    </li>
                    <li class="#">
                      <a href="./inquiry/">{{ t('HEADER_NAV_CONTACT_US') }}</a>
                    </li>
                    <li class="#">
                      <a href="./other/member/">{{ t('HEADER_NAV_MEMBER_SERVICES') }}</a>
                    </li>
                  </ul>
                </div>
              </li>
            </div>
          </div>
          <ul class="nav-btn only_pc">
            <li class="nav-mypage favorite" v-if="!auth.isAuthenticated">
              <RouterLink :to="PATH_NAME.FAVORITES">
                <img src="@/assets/img/common/icn_nav_member.svg" />
                <span>{{ t('HEADER_NAV_LOGIN') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage favorite">
              <RouterLink :to="PATH_NAME.FAVORITES">
                <img src="@/assets/img/common/icn_header_favorite.svg" />
                <span>{{ t('HEADER_NAV_FAVORITES') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage bid">
              <RouterLink :to="PATH_NAME.BIDS">
                <img src="@/assets/img/common/icn_bid.svg" class="bid" />
                <span>{{ t('HEADER_NAV_BIDDING') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage bidded">
              <RouterLink :to="PATH_NAME.BID_HISTORY">
                <img src="@/assets/img/common/icn_bidded.svg" class="bidded" />
                <span>{{ t('HEADER_NAV_SUCCESSFUL_BID_HISTORY') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage registration" v-if="!auth.isAuthenticated">
              <RouterLink :to="PATH_NAME.COGNITO_REGISTER">
                <img src="@/assets/img/common/icn_registration.svg" class="bidded" />
                <span>{{ t('HEADER_NAV_REGISTER') }}</span>
              </RouterLink>
            </li>
            <li class="nav-mypage favorite" v-if="auth.isAuthenticated">
              <a @click="handleLogout" class="auth-button">
                <img src="@/assets/img/common/icn_nav_logout.svg" />
                <span>{{ t('HEADER_NAV_LOGOUT') }}</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- gNav PC/SP end -->
    <!-- gNav SP start -->
    <div class="gNav only_sp">
      <nav>
        <ul class="only_sp">
          <li class="account">
            <button class="btn entry">{{ t('HEADER_NAV_NEW_MEMBER_REGISTER') }}</button>
            <button class="btn login">{{ t('HEADER_NAV_LOGIN') }}</button>
          </li>
          <li class="search">
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="search-keyword"
              :placeholder="t('HEADER_NAV_SEARCH_PLACEHOLDER')"
            />
            <button><img src="@/assets/img/common/icn_search.svg" /></button>
          </li>
          <li class="nav-black">
            <p>{{ t('HEADER_NAV_PRODUCT_CATEGORY') }}</p>
            <ul>
              <li>
                <a href="/list_watch/">{{ t('HEADER_NAV_ASCENDING_AUCTION') }}</a>
              </li>
              <li>
                <a href="/list_bag/">{{ t('HEADER_NAV_SEALED_AUCTION') }}</a>
              </li>
              <li><a href="/list_bag/">ショルダーバッグ</a></li>
              <li><a href="/list_bag/">トートバッグ</a></li>
              <li><a href="/list_bag/">ポーチ</a></li>
              <li><a href="/list_bag/">セカンドバッグ</a></li>
              <li><a href="/list_bag/">ハンドバッグ</a></li>
              <li><a href="/list_bag/">財布</a></li>
              <li><a href="/list_bag/">小物</a></li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ t('HEADER_NAV_MEMBER_MENU') }}</p>
            <ul>
              <li>
                <a href="A">{{ t('HEADER_NAV_MY_PAGE') }}</a>
              </li>
              <li>
                <a href="A">{{ t('HEADER_NAV_LOGIN') }}</a>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ t('HEADER_NAV_SITE_ABOUT') }}</p>
            <ul>
              <li>
                <a href="A">{{ t('HEADER_NAV_SHOPPING_GUIDE') }}</a>
              </li>
              <li>
                <a href="A">{{ t('HEADER_NAV_MEMBER_SERVICES') }}</a>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ t('HEADER_NAV_GUIDANCE') }}</p>
            <ul>
              <li>
                <a href="/entryinfo.html">{{ t('HEADER_NAV_CONTACT_US') }}</a>
              </li>
              <li>
                <a href="/login.html">{{ t('HEADER_NAV_FAQ') }}</a>
              </li>
              <li>
                <a href="/mypage.html" target="_blank">{{ t('HEADER_NAV_COMPANY_INFO') }}</a>
              </li>
              <li>
                <a href="./terms.html">{{ t('HEADER_NAV_TERMS_OF_SERVICE') }}</a>
              </li>
            </ul>
          </li>
        </ul>
        <div class="line-logo">
          <div class="cont-wrap">
            <div class="pct">
              <RouterLink :to="PATH_NAME.TOP">
                <img src="@/assets/img/common/logo_cecauction.svg" />
              </RouterLink>
            </div>
            <div class="sns">
              <ul>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_facebook.svg" class="facebook"
                  /></a>
                </li>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_x.svg" class="x" /></a>
                </li>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_instagram.svg" class="instagram"
                  /></a>
                </li>
                <li>
                  <a href="A" class=""
                    ><img src="@/assets/img/common/icn_sns_youtube.svg" class="youtube"
                  /></a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="line-copyright">
          <div class="cont-wrap">
            <ul>
              <li>
                <a href="./">{{ t('HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW') }}</a>
              </li>
              <li>
                <a href="./">{{ t('HEADER_NAV_PRIVACY_POLICY') }}</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
    <!-- gNav SP end -->
  </header>
</template>

<style lang="css" scoped>
  .nav-elm {
    align-items: center;
  }
  .info-menu {
    width: 120px !important;
  }

  .auth-button {
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .auth-button:hover {
    opacity: 0.8;
  }

  button.auth-button {
    background: none;
    border: none;
    font: inherit;
    color: inherit;
    text-decoration: none;
  }

  a.auth-button {
    text-decoration: none;
  }
</style>
