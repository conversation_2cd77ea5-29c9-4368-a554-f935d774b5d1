module.exports = {
  exhibition_localized_json: {
    LIST_CHECK: true,
    ELEMENT_RULES: {
      exhibition_name: {
        REQUIRED_CHECK: true,
        REQUIRED_ERROR_MESSAGE: 'E000202',
        MAX_LENGTH_CHECK: true,
        MAX_LENGTH: 30,
        MAX_LENGTH_ERROR_MESSAGE: 'E000226',
      },
    },
  },
  previewStartDateTimeVali: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000207',
  },
  compareWithPreviewStartAndNow: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000233',
  },
  compareWithPreviewStartAndStart: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000218',
  },
  compareOneMinuteWithPreviewStartAndStart: {
    PATTERN_CHECK: true,
    PATTERN: /^-[0-9]+$|^[1-9]$|^[0-9]{2,}$/,
    PATTERN_ERROR_MESSAGE: 'E000222',
  },
  unselectOneLaterYearPreviewStartDate: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000260',
  },
  start_datetime: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E000203',
  },
  startDateTimeVali: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000205',
  },
  compareWithStartAndNow: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000234',
  },
  unselectOneLaterYearStartDate: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000261',
  },
  end_datetime: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E000204',
  },
  endDateTimeVali: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000206',
  },
  compareWithEndAndNow: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000235',
  },
  compareWithStartAndEnd: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000219',
  },
  unselectOneLaterYearEndDate: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000262',
  },
  compareOneMinuteWithStartAndEnd: {
    PATTERN_CHECK: true,
    PATTERN: /^-[0-9]+$|^[1-9]$|^[0-9]{2,}$/,
    PATTERN_ERROR_MESSAGE: 'E000223',
  },
  compareWithPreviewEndAndNow: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000236',
  },
  previewEndDateTimeVali: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000208',
  },
  comparePreviewEndDate: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000221',
  },
  comparePreviewEndDate2: {
    PATTERN_CHECK: true,
    PATTERN: /^-[0-9]+$|^[1-9]$|^[0-9]{2,}$/,
    PATTERN_ERROR_MESSAGE: 'E000225',
  },
  unselectOneLaterYearPreviewEndDate: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000263',
  },
  pitch_width: {
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E000217',
  },
  extend_judge_minutes: {
    REQUIRED_CHECK: false,
    REQUIRED_ERROR_MESSAGE: 'E000210',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 3,
    MAX_LENGTH_ERROR_MESSAGE: 'E000214',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E000214',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E000229',
  },
  extend_minutes: {
    REQUIRED_CHECK: false,
    REQUIRED_ERROR_MESSAGE: 'E000211',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 3,
    MAX_LENGTH_ERROR_MESSAGE: 'E000215',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E000215',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E000230',
  },
  max_extend_datetime: {
    REQUIRED_CHECK: false,
    REQUIRED_ERROR_MESSAGE: 'E000212',
  },
  extendDateTimeVali: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000209',
  },
  compareWithExtendAndNow: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000237',
  },
  compareWithEndAndExtend: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000220',
  },
  unselectOneLaterYearMaxExtendDate: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E000264',
  },
  compareOneMinuteWithPreviewEndAndExtend: {
    PATTERN_CHECK: true,
    PATTERN: /^-[0-9]+$|^[1-9]$|^[0-9]{2,}$/,
    PATTERN_ERROR_MESSAGE: 'E000224',
  },
  more_little_judge_pitch: {
    REQUIRED_CHECK: false,
    REQUIRED_ERROR_MESSAGE: 'E000213',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 3,
    MAX_LENGTH_ERROR_MESSAGE: 'E000216',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E000216',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E000231',
  },
  category: {
    REQUIRED_CHECK: false,
    REQUIRED_ERROR_MESSAGE: 'E000239',
  },
  showBidCountFlag: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E000242',
  },
}
